import React from 'react';

const EmailList = ({ messages, onSelectMessage, selectedMessageId }) => {
  if (!messages || messages.length === 0) {
    return <div>No messages found</div>;
  }

  return (
    <div style={{ border: '1px solid #ccc', maxHeight: '500px', overflow: 'auto' }}>
      {messages.map((message) => (
        <div
          key={message.id}
          onClick={() => onSelectMessage(message.id)}
          style={{
            padding: '10px',
            borderBottom: '1px solid #eee',
            cursor: 'pointer',
            backgroundColor: selectedMessageId === message.id ? '#e3f2fd' : 'white'
          }}
          onMouseEnter={(e) => {
            if (selectedMessageId !== message.id) {
              e.target.style.backgroundColor = '#f5f5f5';
            }
          }}
          onMouseLeave={(e) => {
            if (selectedMessageId !== message.id) {
              e.target.style.backgroundColor = 'white';
            }
          }}
        >
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
            Message ID: {message.id}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            Click to view details
          </div>
        </div>
      ))}
    </div>
  );
};

export default EmailList;