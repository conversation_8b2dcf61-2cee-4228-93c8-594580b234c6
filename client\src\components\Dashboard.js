import React, { useState, useEffect } from 'react';
import <PERSON>ail<PERSON>ist from './EmailList';

const Dashboard = ({ user, onLogout }) => {
  const [messages, setMessages] = useState([]);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [loading, setLoading] = useState(false);

  const fetchMessages = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/email/messages', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
    setLoading(false);
  };

  const fetchMessage = async (messageId) => {
    try {
      const response = await fetch(`/api/email/messages/${messageId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setSelectedMessage(data);
      }
    } catch (error) {
      console.error('Error fetching message:', error);
    }
  };

  useEffect(() => {
    fetchMessages();
  }, []);

  return (
    <div style={{ padding: '20px' }}>
      <header style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h1>Sprout - Family Activity Monitor</h1>
        <div>
          <span>Welcome, {user?.email}</span>
          <button onClick={onLogout} style={{ marginLeft: '10px', padding: '5px 10px' }}>
            Logout
          </button>
        </div>
      </header>

      <div style={{ display: 'flex', gap: '20px' }}>
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
            <h2>Recent Messages</h2>
            <button 
              onClick={fetchMessages} 
              disabled={loading}
              style={{ padding: '5px 10px' }}
            >
              {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
          <EmailList 
            messages={messages} 
            onSelectMessage={fetchMessage}
            selectedMessageId={selectedMessage?.id}
          />
        </div>

        {selectedMessage && (
          <div style={{ flex: 1, border: '1px solid #ccc', padding: '15px' }}>
            <h3>Message Details</h3>
            <div style={{ marginBottom: '10px' }}>
              <strong>From:</strong> {selectedMessage.from}
            </div>
            <div style={{ marginBottom: '10px' }}>
              <strong>Subject:</strong> {selectedMessage.subject}
            </div>
            <div style={{ marginBottom: '10px' }}>
              <strong>Date:</strong> {selectedMessage.date}
            </div>
            <div style={{ marginTop: '15px' }}>
              <strong>Content:</strong>
              <div style={{ 
                marginTop: '10px', 
                padding: '10px', 
                backgroundColor: '#f5f5f5', 
                whiteSpace: 'pre-wrap',
                maxHeight: '400px',
                overflow: 'auto'
              }}>
                {selectedMessage.body || selectedMessage.snippet}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;